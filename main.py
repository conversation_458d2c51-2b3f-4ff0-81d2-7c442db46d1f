from crawler import WebCrawler
from data_extractor import extract_text_from_html
import os # For saving HTML file
import json # For JSONL output
import sqlite3 # For SQLite database
import hashlib # For content hashing
from datetime import datetime # For human-readable timestamps
from ai_interface import analyze_text_with_openrouter #, query_ollama_with_context
from rag_system import Rag<PERSON><PERSON>ger
from urllib.parse import urlparse # For the main script's prompt and source_domain

# Import the backend classes
from conceptual_crawler_backends import PlaywrightBackend, SeleniumBackend #, PyppeteerBackend

# Import document processing module
from document_processor import process_document, process_documents, load_document_texts, init_document_tables

# Import the tools manager
from tools_manager import ToolsManager

SQLITE_DB_FILE = "web_content.db"

# Initialize the tools manager
tools_manager = ToolsManager()

def init_db(db_path=SQLITE_DB_FILE):
    """Initializes the SQLite database and creates the content table if it doesn't exist."""
    # Check if database file already exists
    db_existed = os.path.exists(db_path)

    # Connect to the database (creates it if it doesn't exist)
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    # Create the pages table if it doesn't exist
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS pages (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            url TEXT UNIQUE NOT NULL,
            title TEXT,
            extracted_text TEXT,
            content_hash TEXT,
            crawl_timestamp_utc TEXT NOT NULL,
            last_modified_header TEXT,
            status_code INTEGER,
            source_domain TEXT
        )
    ''')

    # Get the number of records in the database
    cursor.execute("SELECT COUNT(*) FROM pages")
    record_count = cursor.fetchone()[0]

    # Initialize document tables
    init_document_tables(db_path)

    # Commit changes and close connection
    conn.commit()
    conn.close()

    # Print appropriate message
    if db_existed:
        print(f"Database {db_path} connected. Contains {record_count} pages.")
    else:
        print(f"Database {db_path} created and initialized.")

def main():
    # Initialize the database again to ensure it exists and is up-to-date
    # This provides double protection in case the module-level initialization is skipped
    init_db()

    # Make sure analyze_text_with_openrouter is available in this function
    # We already imported it at the module level, but let's make sure it's accessible
    from ai_interface import analyze_text_with_openrouter

    # Check if there's existing data in the database
    has_data = check_existing_data()

    # Present initial options to the user
    print("\nWelcome to Fungi Web Crawler & Analyzer!")
    print("Choose an option:")
    print("1. Start a new web crawl")
    print("2. Chat with existing data (RAG Q&A)")

    # Keep asking until we get a valid choice
    initial_choice = None
    while initial_choice not in ['1', '2']:
        initial_choice = input("Enter choice (1 or 2): ")
        if initial_choice not in ['1', '2']:
            print("Invalid choice. Please enter 1 or 2.")

    # If user chooses to chat with existing data
    if initial_choice == '2':
        # Check if we actually have data
        if not has_data:
            print("No existing data found in the database. You need to crawl a website first.")
            print("Switching to crawl mode...")
        else:
            # Go directly to RAG Q&A with existing data
            analyze_existing_data(skip_choice=True)
            return

    # If we're here, user chose to crawl or was redirected due to no data
    start_url = input("Enter the starting URL (e.g., http://example.com): ")
    while True:
        try:
            max_pages_to_crawl_input = input("Enter maximum number of pages to crawl (e.g., 50, default is 20): ").strip()
            max_pages_to_crawl = int(max_pages_to_crawl_input) if max_pages_to_crawl_input else 20
            if max_pages_to_crawl <= 0: raise ValueError("Max pages must be positive.")
            break
        except ValueError as e:
            print(f"Invalid input. Please enter a positive number or press Enter for default. ({e})")
    # A more robust solution would use argparse or a config file
    # For OpenRouter, ensure OPENROUTER_API_KEY is set in ai_interface.py or via environment variables

    backends_to_try = []
    # Using a more current and common user agent
    user_agent_string = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    crawl_delay = 0.75 # Slightly increased default delay

    # Default order: Playwright first, then Selenium as fallback
    print("Configuring Playwright as the primary backend.")
    backends_to_try.append(PlaywrightBackend(user_agent=user_agent_string, delay_seconds=crawl_delay, headless=True))

    print("Configuring Selenium as a fallback backend.")
    driver_path_selenium = input(
        "Enter path to ChromeDriver for Selenium (optional, press Enter to use automatic download via webdriver-manager): "
    ).strip()
    try:
        # Initialize SeleniumBackend; setup_browser will be called by the crawler.
        # If driver_path_selenium is empty, SeleniumBackend will attempt to use webdriver-manager.
        backends_to_try.append(SeleniumBackend(user_agent=user_agent_string,
                                               delay_seconds=crawl_delay,
                                               headless=True,
                                               driver_path=driver_path_selenium if driver_path_selenium else None))
    except Exception as e: # Catch errors during instantiation (e.g., if webdriver-manager itself has an issue not caught inside)
        print(f"Could not initialize Selenium fallback backend instance: {e}. Proceeding without it if Playwright also fails setup.")

    if not backends_to_try: # Should not happen with current logic unless initial backend init fails badly
        print("No backends were configured. Exiting.")
        return

    # Get allowed paths from the user
    print("Enter allowed subdomain paths (optional):")
    print("This will restrict crawling to URLs that contain these paths.")
    print("For example, to only crawl pages under /docs/, enter: /docs/")
    print("For multiple paths, enter them as a JSON array: ['/docs/', '/blog/']")
    print("Leave empty to crawl all pages in the domain.")

    allowed_paths_input = input("Enter allowed paths (or press Enter to crawl all): ").strip()

    # Parse the allowed paths
    allowed_paths = []
    if allowed_paths_input:
        try:
            # Check if it's a JSON array
            if allowed_paths_input.startswith('[') and allowed_paths_input.endswith(']'):
                allowed_paths = json.loads(allowed_paths_input)
                print(f"Using {len(allowed_paths)} allowed paths: {allowed_paths}")
            else:
                # Treat as a single path
                allowed_paths = [allowed_paths_input]
                print(f"Using allowed path: {allowed_paths_input}")
        except json.JSONDecodeError:
            print(f"Error parsing JSON input. Using as a single path: {allowed_paths_input}")
            allowed_paths = [allowed_paths_input]

    print(f"\nStarting crawl for {start_url} with up to {len(backends_to_try)} backend(s) configured in sequence.")
    print(f"Allowed paths: {allowed_paths if allowed_paths else 'All paths in domain'}")

    crawler = WebCrawler(start_url=start_url, backends=backends_to_try, allowed_paths=allowed_paths)
    crawled_pages_data = crawler.crawl(max_pages=max_pages_to_crawl)

    if not crawled_pages_data:
        print("No data crawled.")
        return

    print(f"\nCrawled {len(crawled_pages_data)} pages. Extracting text...")

    extracted_texts_for_rag = []
    full_text_corpus = [] # Use a list to join later, more efficient
    # processed_data_for_storage = [] # No longer needed for JSONL

    for i, raw_page_data in enumerate(crawled_pages_data):
        print(f"\n--- Processing page {i+1}/{len(crawled_pages_data)}: {raw_page_data['url']} ---")
        raw_html = raw_page_data.get('html', '')
        print(f"Raw HTML length: {len(raw_html)} characters")

        # Optionally save the first page's HTML for inspection
        if i == 0 and raw_html:
            try:
                debug_html_filename = "debug_first_page.html"
                with open(debug_html_filename, "w", encoding="utf-8") as f:
                    f.write(raw_html)
                print(f"Saved raw HTML of the first page to '{debug_html_filename}'")
            except Exception as e:
                print(f"Could not save {debug_html_filename}: {e}")

        extracted_text = extract_text_from_html(raw_html) # Use raw_html from raw_page_data
        print(f"Extracted text length: {len(extracted_text)} characters")
        print(f"Extracted text (first 200 chars): {extracted_text[:200]}...")

        if extracted_text.strip(): # Only process if there's meaningful text
            content_hash = hashlib.md5(extracted_text.encode('utf-8')).hexdigest()

            page_metadata = {
                "url": raw_page_data['url'],
                "title": raw_page_data.get('title', ''),
                "extracted_text": extracted_text,
                "content_hash": content_hash,
                "crawl_timestamp_utc": datetime.utcfromtimestamp(raw_page_data['crawl_timestamp']).isoformat() + "Z",
                "last_modified_header": raw_page_data.get('last_modified_header'),
                "status_code": raw_page_data.get('status_code'),
                "source_domain": urlparse(raw_page_data['url']).netloc
            }
            # Insert or update data in SQLite
            conn = sqlite3.connect(SQLITE_DB_FILE)
            cursor = conn.cursor()
            try:
                cursor.execute('''
                    INSERT INTO pages (url, title, extracted_text, content_hash, crawl_timestamp_utc, last_modified_header, status_code, source_domain)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    ON CONFLICT(url) DO UPDATE SET
                        title=excluded.title,
                        extracted_text=excluded.extracted_text,
                        content_hash=excluded.content_hash,
                        crawl_timestamp_utc=excluded.crawl_timestamp_utc,
                        last_modified_header=excluded.last_modified_header,
                        status_code=excluded.status_code
                ''', (page_metadata['url'], page_metadata['title'], page_metadata['extracted_text'], page_metadata['content_hash'],
                      page_metadata['crawl_timestamp_utc'], page_metadata['last_modified_header'], page_metadata['status_code'], page_metadata['source_domain']))
                conn.commit()
                print(f"Data for {page_metadata['url']} saved to database.")
            except sqlite3.Error as e:
                print(f"SQLite error inserting/updating data for {page_metadata['url']}: {e}")
            finally:
                conn.close()

            extracted_texts_for_rag.append(extracted_text) # Still needed for RAG
            full_text_corpus.append(extracted_text) # Still needed for direct analysis

    print("\nChoose analysis method:")
    print("1. RAG-based Q&A with OpenRouter (builds an index first, requires API key)")
    print("2. RAG-based Q&A without LLM (fast retrieval only, no API key needed)")

    # Keep asking until we get a valid choice
    choice = None
    while choice not in ['1', '2']:
        choice = input("Enter choice (1 or 2): ")
        if choice not in ['1', '2']:
            print("Invalid choice. Please enter 1 or 2.")

    if choice == '1' or choice == '2':
        use_llm = (choice == '1')  # Use LLM for option 1, retrieval-only for option 2

        if use_llm:
            print("\nSetting up RAG system with LLM...")
            # Check if API key is set
            api_key = os.getenv("OPENROUTER_API_KEY")
            if not api_key:
                print("Warning: OpenRouter API key not set. Set the OPENROUTER_API_KEY environment variable.")
                print("Continuing without LLM (retrieval-only mode)...")
                use_llm = False
            else:
                # Make sure the API key is also set in the ai_interface module
                from ai_interface import set_openrouter_api_key
                key_set = set_openrouter_api_key(api_key)
                print(f"API key loaded. Length: {len(api_key)}, Valid: {key_set}")
                print(f"LLM mode is {'enabled' if use_llm else 'disabled'}")
        else:
            print("\nSetting up RAG system in retrieval-only mode (no LLM)...")

        rag_manager = RagManager()
        # Filter out empty strings before building the index
        filtered_texts = list(filter(None, [t.strip() for t in extracted_texts_for_rag]))
        # Get the URLs for each page
        urls = [page_data.get('url', f"page_{i}") for i, page_data in enumerate(crawled_pages_data) if page_data.get('html')]
        # Build the index with texts and URLs
        rag_manager.build_index(filtered_texts, urls)

        if rag_manager.vector_store:
            while True:
                user_query = input("\nAsk a question about the website content (or type 'exit' to quit): ")
                if user_query.lower() == 'exit':
                    break

                # Check if this is a special command to set the API key
                if user_query.startswith('set_api_key:'):
                    api_key = user_query.replace('set_api_key:', '')

                    # Set the API key in the environment variable
                    os.environ["OPENROUTER_API_KEY"] = api_key

                    # Also set it in the ai_interface module
                    from ai_interface import set_openrouter_api_key
                    key_set = set_openrouter_api_key(api_key)

                    print(f"API key set successfully. Length: {len(api_key)}, Valid: {key_set}")

                    # Update the use_llm flag if we now have an API key
                    if api_key:
                        use_llm = True
                        print("LLM mode enabled with the provided API key.")
                    continue

                # Check if this is a Pinecone configuration command
                if user_query.startswith('pinecone_config:'):
                    config_str = user_query.replace('pinecone_config:', '')
                    try:
                        config = json.loads(config_str)
                        api_key = config.get('api_key')
                        environment = config.get('environment')
                        index_name = config.get('index_name')
                        use_pinecone = config.get('use_pinecone', False)

                        print(f"Configuring Pinecone with API key (length: {len(api_key) if api_key else 0}), environment: {environment}, index: {index_name}")

                        # Configure Pinecone in the RAG manager
                        success = rag_manager.configure_pinecone(api_key, environment, index_name, use_pinecone)

                        if success:
                            print("Pinecone configuration successful")
                        else:
                            print("Failed to configure Pinecone")
                        continue
                    except Exception as e:
                        print(f"Error configuring Pinecone: {e}")
                        continue

                # Check if this is a Pinecone test connection command
                if user_query == 'pinecone_test_connection':
                    print("Testing Pinecone connection...")
                    if not rag_manager.pinecone_manager or not rag_manager.pinecone_manager.is_initialized:
                        print("Pinecone not configured. Cannot test connection.")
                    else:
                        # Test connection by getting stats
                        stats = rag_manager.get_pinecone_stats()
                        if stats:
                            print("Pinecone connection successful")
                            print(f"Index stats: {stats}")
                        else:
                            print("Failed to connect to Pinecone")
                    continue

                # Check if this is a Pinecone upload command
                if user_query == 'pinecone_upload_vectors':
                    print("Uploading vectors to Pinecone...")
                    if not rag_manager.pinecone_manager or not rag_manager.pinecone_manager.is_initialized:
                        print("Pinecone not configured. Cannot upload vectors.")
                    else:
                        # Upload vectors
                        success, count = rag_manager.upload_to_pinecone()
                        if success:
                            print(f"Successfully uploaded {count} vectors to Pinecone")
                        else:
                            print("Failed to upload vectors to Pinecone")
                    continue

                # Check if this is a Pinecone switch command
                if user_query.startswith('pinecone_switch:'):
                    use_pinecone_str = user_query.replace('pinecone_switch:', '')
                    use_pinecone = use_pinecone_str.lower() == 'true'

                    print(f"Switching vector store to {'Pinecone' if use_pinecone else 'local FAISS'}...")
                    success = rag_manager.switch_to_pinecone(use_pinecone)

                    if success:
                        print(f"Switched to {'Pinecone' if use_pinecone else 'local FAISS'} vector store")
                    else:
                        print(f"Failed to switch to {'Pinecone' if use_pinecone else 'local FAISS'} vector store")
                    continue

                # Check if this is a Pinecone stats command
                if user_query == 'pinecone_get_stats':
                    print("Getting Pinecone stats...")
                    if not rag_manager.pinecone_manager or not rag_manager.pinecone_manager.is_initialized:
                        print("Pinecone not configured. Cannot get stats.")
                    else:
                        # Get stats
                        stats = rag_manager.get_pinecone_stats()
                        if stats:
                            print(f"Pinecone stats: {stats}")
                        else:
                            print("Failed to get Pinecone stats")
                    continue

                # Check if this is a tools query command
                if user_query.startswith('tools_query:'):
                    try:
                        # Format: tools_query:query_text:k
                        parts = user_query.replace('tools_query:', '').split(':', 1)

                        if len(parts) >= 1:
                            query_text = parts[0]
                            k = int(parts[1]) if len(parts) > 1 else 3

                            print(f"Tools Knowledge Query: {query_text}")
                            print(f"Number of results: {k}")

                            # Query the tools knowledge base
                            answer = query_tools_knowledge(query_text, k=k)
                            print(f"Answer:\n{answer}")
                        else:
                            print("Error: Invalid tools_query command format")
                    except Exception as e:
                        print(f"Error handling tools query: {e}")
                    continue

                # Check if this is a command to analyze a document with LLM
                if user_query.startswith('analyze_document:'):
                    # Format: analyze_document:document_id:model_index:query
                    parts = user_query.replace('analyze_document:', '').split(':', 2)
                    if len(parts) >= 3:
                        document_id = parts[0]
                        model_index = int(parts[1])
                        doc_query = parts[2]

                        # Get the model name based on the index
                        model_names = [
                            "mistralai/mistral-7b-instruct",
                            "google/gemini-2.0-flash-001",
                            "qwen/qwen3-235b-a22b:free",
                            "meta-llama/llama-4-maverick:free",
                            "google/gemini-2.0-flash-exp:free"
                        ]

                        model_name = model_names[model_index] if 0 <= model_index < len(model_names) else "mistralai/mistral-7b-instruct"

                        print(f"DIRECT DOCUMENT ANALYSIS: Analyzing document {document_id} with model {model_name}")
                        print(f"Query: {doc_query}")
                        print("This is a direct LLM analysis that completely bypasses the RAG system")

                        # We already imported analyze_text_with_openrouter at the beginning of the function

                        # Make sure we have an API key
                        api_key = os.getenv("OPENROUTER_API_KEY")
                        if not api_key:
                            print("Error: OpenRouter API key not set. Set the OPENROUTER_API_KEY environment variable.")
                            answer = "Error: OpenRouter API key not set. Please set up your API key in settings."
                        else:
                            # Set the API key in the ai_interface module
                            from ai_interface import set_openrouter_api_key
                            key_set = set_openrouter_api_key(api_key)

                            if not key_set:
                                print("Error: Failed to set OpenRouter API key.")
                                answer = "Error: Failed to set OpenRouter API key."
                            else:
                                # Analyze the document with LLM - DIRECT ANALYSIS, NOT USING RAG
                                print("Performing direct document analysis with LLM (bypassing RAG system)")
                                answer = analyze_document_with_llm(
                                    document_id,
                                    doc_query,
                                    llm_interface_func=analyze_text_with_openrouter,
                                    llm_model_name=model_name
                                )

                        # Ensure the answer starts with "Answer:" for the UI
                        if not answer.startswith("Answer:") and not answer.startswith("Based on the document:"):
                            answer = "Answer: " + answer

                        print(f"Answer:\n{answer}")
                        continue
                    else:
                        print("Error: Invalid analyze_document command format")
                        continue

                # For RAG with LLM, use OpenRouter
                if use_llm:
                    model_for_rag = "mistralai/mistral-7b-instruct"
                    print("Querying with LLM (this may take a moment)...")
                    answer = rag_manager.query_rag(
                        user_query,
                        llm_interface_func=analyze_text_with_openrouter,
                        llm_model_name=model_for_rag,
                        site_url=start_url,
                        use_llm=True
                    )
                # For retrieval-only mode
                else:
                    print("Retrieving relevant information...")
                    answer = rag_manager.query_rag(
                        user_query,
                        use_llm=False
                    )

                print(f"Answer:\n{answer}")
    else:
        print("Invalid choice.")

# Initialize the database at module level to ensure it exists
# This ensures the database is created even if the script is imported
init_db()

def get_db_modification_time(db_path=SQLITE_DB_FILE):
    """
    Get the modification time of the database file.

    Args:
        db_path: Path to the SQLite database file

    Returns:
        float: The modification time of the database file, or 0 if it doesn't exist
    """
    if not os.path.exists(db_path):
        return 0

    return os.path.getmtime(db_path)

def save_research_to_database(research_data, db_path=SQLITE_DB_FILE):
    """
    Save research data to the database.

    Args:
        research_data: A dictionary containing research data with the following keys:
            - query: The search query
            - timestamp: The timestamp of the research
            - sources: A list of source URLs
            - content: A list of dictionaries with url, title, and content keys
        db_path: Path to the SQLite database file

    Returns:
        bool: True if the data was saved successfully, False otherwise
    """
    try:
        # Connect to the database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Create research table if it doesn't exist
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS research (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            query TEXT NOT NULL,
            timestamp TEXT NOT NULL,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP
        )
        """)

        # Create research_sources table if it doesn't exist
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS research_sources (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            research_id INTEGER NOT NULL,
            url TEXT NOT NULL,
            title TEXT,
            content TEXT,
            FOREIGN KEY (research_id) REFERENCES research (id) ON DELETE CASCADE
        )
        """)

        # Insert research data
        cursor.execute(
            "INSERT INTO research (query, timestamp) VALUES (?, ?)",
            (research_data['query'], research_data['timestamp'])
        )
        research_id = cursor.lastrowid

        # Insert research sources
        for item in research_data['content']:
            cursor.execute(
                "INSERT INTO research_sources (research_id, url, title, content) VALUES (?, ?, ?, ?)",
                (research_id, item['url'], item['title'], item['content'])
            )

        # Commit changes
        conn.commit()
        print(f"Research data saved to database with ID: {research_id}")
        return True
    except Exception as e:
        print(f"Error saving research data to database: {e}")
        if conn:
            conn.rollback()
        return False
    finally:
        if conn:
            conn.close()

def check_existing_data(db_path=SQLITE_DB_FILE, print_output=False):
    """
    Check if there is existing data in the database.

    Args:
        db_path: Path to the SQLite database file
        print_output: If True, print the result (for command-line use)

    Returns:
        bool: True if the database exists and contains data, False otherwise
    """
    if not os.path.exists(db_path):
        if print_output:
            print("existing_data:false")
        return False

    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    try:
        # Check if the pages table exists and has data
        cursor.execute("SELECT COUNT(*) FROM pages")
        count = cursor.fetchone()[0]

        if count > 0:
            if print_output:
                print(f"existing_data:true:{count}")
            return True
        else:
            if print_output:
                print("existing_data:false")
            return False
    except sqlite3.Error:
        if print_output:
            print("existing_data:false")
        return False
    finally:
        conn.close()

def import_documents(file_paths, db_path=SQLITE_DB_FILE, options=None):
    """
    Import documents into the database and process them for RAG.

    Args:
        file_paths: List of paths to document files
        db_path: Path to the SQLite database
        options: Dictionary of import options
            - store_original_paths: Whether to store original file paths (default: True)
            - analyze_after_import: Whether to analyze documents after import (default: True)

    Returns:
        list: List of document metadata dictionaries
    """
    print(f"Importing {len(file_paths)} documents...")

    # Set default options if not provided
    if options is None:
        options = {
            'store_original_paths': True,
            'analyze_after_import': True
        }

    # Process the documents
    results = process_documents(file_paths, db_path, options)

    if results:
        print(f"Successfully imported {len(results)} documents.")

        # Print some information about the imported documents
        for doc in results:
            print(f"Imported: {doc['filename']} ({doc['file_type']}, {doc['file_size']} bytes)")
    else:
        print("No documents were successfully imported.")

    return results

def load_existing_data(db_path=SQLITE_DB_FILE, include_documents=True):
    """
    Load existing data from the database for analysis.

    Args:
        db_path: Path to the SQLite database
        include_documents: Whether to include document data

    Returns:
        tuple: (sources, extracted_texts)
    """
    if not os.path.exists(db_path):
        print("No database found.")
        return [], []

    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    try:
        # Get all extracted text from the database with URLs and titles
        cursor.execute("""
            SELECT url, extracted_text, title, source_domain
            FROM pages
            WHERE extracted_text IS NOT NULL AND LENGTH(extracted_text) > 0
        """)
        web_rows = cursor.fetchall()

        # Initialize lists
        sources = []
        extracted_texts = []

        # Process web data
        if web_rows:
            web_urls = [row[0] for row in web_rows]
            web_texts = [row[1] for row in web_rows]

            sources.extend(web_urls)
            extracted_texts.extend(web_texts)

            # Print some information about the loaded web data
            print(f"Loaded {len(web_texts)} pages from the database.")
            domains = set([row[3] for row in web_rows if row[3]])
            if domains:
                print(f"Domains in the database: {', '.join(domains)}")

        # If requested, also load document data
        if include_documents:
            doc_ids, doc_texts = load_document_texts(db_path)

            if doc_ids:
                # Add document IDs as sources (prefixed to distinguish from URLs)
                doc_sources = [f"document:{doc_id}" for doc_id in doc_ids]

                sources.extend(doc_sources)
                extracted_texts.extend(doc_texts)

                print(f"Loaded {len(doc_texts)} documents from the database.")

        if not sources:
            print("No data found in the database.")

        return sources, extracted_texts
    except sqlite3.Error as e:
        print(f"Error loading data from database: {e}")
        return [], []
    finally:
        conn.close()

def analyze_existing_data(skip_choice=False):
    """
    Load and analyze existing data from the database.

    Args:
        skip_choice: If True, skip the analysis method choice and go directly to RAG Q&A
    """
    # Import the OpenRouter interface at the beginning of the function
    # to ensure it's available for all code paths
    from ai_interface import analyze_text_with_openrouter

    print("Loading existing data from database...")
    urls, extracted_texts = load_existing_data()

    if not extracted_texts:
        print("No data to analyze.")
        return

    print(f"Loaded {len(extracted_texts)} pages from the database.")

    # Get the domain from the first URL
    start_url = urls[0] if urls else "unknown"

    # If skip_choice is True, go directly to RAG Q&A in retrieval-only mode
    choice = '2' if skip_choice else None

    if not skip_choice:
        print("\nChoose analysis method:")
        print("1. RAG-based Q&A with OpenRouter (builds an index first, requires API key)")
        print("2. RAG-based Q&A without LLM (fast retrieval only, no API key needed)")

        # Keep asking until we get a valid choice
        while choice not in ['1', '2']:
            choice = input("Enter choice (1 or 2): ")
            if choice not in ['1', '2']:
                print("Invalid choice. Please enter 1 or 2.")

    if choice == '1' or choice == '2':
        use_llm = (choice == '1')  # Use LLM for option 1, retrieval-only for option 2

        if use_llm:
            print("\nSetting up RAG system with LLM...")
            # Check if API key is set
            api_key = os.getenv("OPENROUTER_API_KEY")
            if not api_key:
                print("Warning: OpenRouter API key not set. Set the OPENROUTER_API_KEY environment variable.")
                print("Continuing without LLM (retrieval-only mode)...")
                use_llm = False
            else:
                # Make sure the API key is also set in the ai_interface module
                from ai_interface import set_openrouter_api_key
                key_set = set_openrouter_api_key(api_key)
                print(f"API key loaded. Length: {len(api_key)}, Valid: {key_set}")
                print(f"LLM mode is {'enabled' if use_llm else 'disabled'}")
        else:
            print("\nSetting up RAG system in retrieval-only mode (no LLM)...")

        # Create cache directory in the same directory as the database
        db_dir = os.path.dirname(os.path.abspath(SQLITE_DB_FILE))
        cache_dir = os.path.join(db_dir, 'rag_cache')

        # Initialize RAG manager with cache directory
        rag_manager = RagManager(cache_dir=cache_dir)

        # Filter out empty strings before building the index
        filtered_texts = list(filter(None, [t.strip() for t in extracted_texts]))

        # Get the database modification time
        db_mod_time = get_db_modification_time()

        # Build the index with texts, URLs, and DB modification time (will use cache if available)
        rag_manager.build_index(filtered_texts, urls, db_mod_time)

        if rag_manager.vector_store:
            while True:
                line = input("\nAsk a question about the website content (or type 'exit' to quit): ")

                # Check for document context command
                if line.startswith('document_context:'):
                    command_data = line.replace('document_context:', '', 1)
                    handle_document_context_command(command_data, rag_manager)
                    continue
                # Check for exit command
                elif line.lower() == 'exit':
                    break

                # Regular query
                user_query = line

                # Check if this is a special command to set the API key
                if user_query.startswith('set_api_key:'):
                    api_key = user_query.replace('set_api_key:', '')

                    # Set the API key in the environment variable
                    os.environ["OPENROUTER_API_KEY"] = api_key

                    # Also set it in the ai_interface module
                    from ai_interface import set_openrouter_api_key
                    key_set = set_openrouter_api_key(api_key)

                    print(f"API key set successfully. Length: {len(api_key)}, Valid: {key_set}")

                    # Update the use_llm flag if we now have an API key
                    if api_key:
                        use_llm = True
                        print("LLM mode enabled with the provided API key.")
                    continue

                # Check if this is a Pinecone configuration command
                if user_query.startswith('pinecone_config:'):
                    config_str = user_query.replace('pinecone_config:', '')
                    try:
                        config = json.loads(config_str)
                        api_key = config.get('api_key')
                        environment = config.get('environment')
                        index_name = config.get('index_name')
                        use_pinecone = config.get('use_pinecone', False)

                        print(f"Configuring Pinecone with API key (length: {len(api_key) if api_key else 0}), environment: {environment}, index: {index_name}")

                        # Configure Pinecone in the RAG manager
                        success = rag_manager.configure_pinecone(api_key, environment, index_name, use_pinecone)

                        if success:
                            print("Pinecone configuration successful")
                        else:
                            print("Failed to configure Pinecone")
                        continue
                    except Exception as e:
                        print(f"Error configuring Pinecone: {e}")
                        continue

                # Check if this is a Pinecone test connection command
                if user_query == 'pinecone_test_connection':
                    print("Testing Pinecone connection...")
                    if not rag_manager.pinecone_manager or not rag_manager.pinecone_manager.is_initialized:
                        print("Pinecone not configured. Cannot test connection.")
                    else:
                        # Test connection by getting stats
                        stats = rag_manager.get_pinecone_stats()
                        if stats:
                            print("Pinecone connection successful")
                            print(f"Index stats: {stats}")
                        else:
                            print("Failed to connect to Pinecone")
                    continue

                # Check if this is a Pinecone upload command
                if user_query == 'pinecone_upload_vectors':
                    print("Uploading vectors to Pinecone...")
                    if not rag_manager.pinecone_manager or not rag_manager.pinecone_manager.is_initialized:
                        print("Pinecone not configured. Cannot upload vectors.")
                    else:
                        # Upload vectors
                        success, count = rag_manager.upload_to_pinecone()
                        if success:
                            print(f"Successfully uploaded {count} vectors to Pinecone")
                        else:
                            print("Failed to upload vectors to Pinecone")
                    continue

                # Check if this is a Pinecone switch command
                if user_query.startswith('pinecone_switch:'):
                    use_pinecone_str = user_query.replace('pinecone_switch:', '')
                    use_pinecone = use_pinecone_str.lower() == 'true'

                    print(f"Switching vector store to {'Pinecone' if use_pinecone else 'local FAISS'}...")
                    success = rag_manager.switch_to_pinecone(use_pinecone)

                    if success:
                        print(f"Switched to {'Pinecone' if use_pinecone else 'local FAISS'} vector store")
                    else:
                        print(f"Failed to switch to {'Pinecone' if use_pinecone else 'local FAISS'} vector store")
                    continue

                # Check if this is a Pinecone stats command
                if user_query == 'pinecone_get_stats':
                    print("Getting Pinecone stats...")
                    if not rag_manager.pinecone_manager or not rag_manager.pinecone_manager.is_initialized:
                        print("Pinecone not configured. Cannot get stats.")
                    else:
                        # Get stats
                        stats = rag_manager.get_pinecone_stats()
                        if stats:
                            print(f"Pinecone stats: {stats}")
                        else:
                            print("Failed to get Pinecone stats")
                    continue

                # Check if this is a command to set the LLM mode
                if user_query.startswith('use_llm:'):
                    llm_mode = user_query.replace('use_llm:', '').strip().lower()
                    if llm_mode == 'true':
                        use_llm = True
                        print("LLM mode explicitly enabled.")
                    elif llm_mode == 'false':
                        use_llm = False
                        print("LLM mode explicitly disabled (retrieval-only mode).")
                    continue

                # Check if this is a command to analyze a document with LLM
                if user_query.startswith('analyze_document:'):
                    # Format: analyze_document:document_id:model_index:query
                    parts = user_query.replace('analyze_document:', '').split(':', 2)
                    if len(parts) >= 3:
                        document_id = parts[0]
                        model_index = int(parts[1])
                        doc_query = parts[2]

                        # Get the model name based on the index
                        model_names = [
                            "openai/gpt-4o-mini",
                            "openai/gpt-4o",
                            "anthropic/claude-3-opus-20240229",
                            "anthropic/claude-3-sonnet-20240229",
                            "mistralai/mistral-7b-instruct"
                        ]

                        model_name = model_names[model_index] if 0 <= model_index < len(model_names) else "mistralai/mistral-7b-instruct"

                        print(f"Analyzing document {document_id} with model {model_name}")
                        print(f"Query: {doc_query}")

                        # Import the OpenRouter interface directly
                        from ai_interface import analyze_text_with_openrouter

                        # Make sure we have an API key
                        api_key = os.getenv("OPENROUTER_API_KEY")
                        if not api_key:
                            print("Error: OpenRouter API key not set. Set the OPENROUTER_API_KEY environment variable.")
                            answer = "Error: OpenRouter API key not set. Please set up your API key in settings."
                        else:
                            # Set the API key in the ai_interface module
                            from ai_interface import set_openrouter_api_key
                            key_set = set_openrouter_api_key(api_key)

                            if not key_set:
                                print("Error: Failed to set OpenRouter API key.")
                                answer = "Error: Failed to set OpenRouter API key."
                            else:
                                # Analyze the document with LLM - DIRECT ANALYSIS, NOT USING RAG
                                print("Performing direct document analysis with LLM (bypassing RAG system)")
                                answer = analyze_document_with_llm(
                                    document_id,
                                    doc_query,
                                    llm_interface_func=analyze_text_with_openrouter,
                                    llm_model_name=model_name
                                )

                        # Ensure the answer starts with "Answer:" for the UI
                        if not answer.startswith("Answer:") and not answer.startswith("Based on the document:"):
                            answer = "Answer: " + answer

                        print(f"Answer:\n{answer}")
                        continue
                    else:
                        print("Error: Invalid analyze_document command format")
                        continue

                # Check if this is a command to analyze with LLM (Deep Research mode)
                if user_query.startswith('analyze_with_llm:'):
                    model_name = user_query.replace('analyze_with_llm:', '').strip()
                    print(f"Analyzing with LLM model: {model_name}")
                    # Wait for the prompt in the next input
                    continue

                # Check if this is a command for direct LLM analysis (bypassing RAG)
                if user_query.startswith('direct_llm_analysis:'):
                    model_name = user_query.replace('direct_llm_analysis:', '').strip()
                    print(f"Direct LLM analysis with model: {model_name}")
                    # Set a flag to indicate we're doing direct LLM analysis
                    direct_llm_analysis = True
                    # Wait for the prompt in the next input
                    continue

                # Check if this is a command to save research data
                if user_query.startswith('save_research_data:'):
                    try:
                        research_data_json = user_query.replace('save_research_data:', '').strip()
                        research_data = json.loads(research_data_json)

                        # Save the research data to the database
                        save_research_to_database(research_data)
                        print("Research data saved to database successfully")
                    except Exception as e:
                        print(f"Error saving research data: {e}")
                    continue

                # Check if this is a tools query command
                if user_query.startswith('tools_query:'):
                    try:
                        # Format: tools_query:query_text:k
                        parts = user_query.replace('tools_query:', '').split(':', 1)

                        if len(parts) >= 1:
                            query_text = parts[0]
                            k = int(parts[1]) if len(parts) > 1 else 3

                            print(f"Tools Knowledge Query: {query_text}")
                            print(f"Number of results: {k}")

                            # Query the tools knowledge base
                            answer = query_tools_knowledge(query_text, k=k)
                            print(f"Answer:\n{answer}")
                        else:
                            print("Error: Invalid tools_query command format")
                    except Exception as e:
                        print(f"Error handling tools query: {e}")
                    continue

                # Check if we're in direct LLM analysis mode (for Deep Research)
                if 'direct_llm_analysis' in locals() and direct_llm_analysis:
                    # Reset the flag
                    direct_llm_analysis = False

                    # Use the LLM directly without RAG
                    model_for_analysis = model_name if 'model_name' in locals() else "mistralai/mistral-7b-instruct"
                    print(f"Performing direct LLM analysis with model {model_for_analysis} (this may take a moment)...")

                    # Use the OpenRouter API directly
                    answer = analyze_text_with_openrouter(
                        user_query,
                        model_name=model_for_analysis,
                        system_message_content="You are a helpful assistant that analyzes and summarizes information from multiple sources."
                    )

                    # Ensure the answer starts with "Answer:" for the UI
                    if not answer.startswith("Answer:"):
                        answer = "Answer: " + answer
                # For RAG with LLM, use OpenRouter
                elif use_llm:
                    model_for_rag = "mistralai/mistral-7b-instruct"
                    print("Querying with LLM (this may take a moment)...")
                    answer = rag_manager.query_rag(
                        user_query,
                        llm_interface_func=analyze_text_with_openrouter,
                        llm_model_name=model_for_rag,
                        site_url=start_url,
                        use_llm=True
                    )
                # For retrieval-only mode
                else:
                    print("Retrieving relevant information...")
                    answer = rag_manager.query_rag(
                        user_query,
                        use_llm=False
                    )

                print(f"Answer:\n{answer}")

def direct_analysis(prompt_file, model_index=0):
    """
    Perform direct analysis of content using the LLM without RAG.

    Args:
        prompt_file: Path to a file containing the prompt to analyze
        model_index: Index of the model to use (0-4)
    """
    try:
        # Check if API key is set
        api_key = os.getenv("OPENROUTER_API_KEY")
        if not api_key:
            print("Error: OpenRouter API key not set. Set the OPENROUTER_API_KEY environment variable.")
            return

        # Make sure the API key is also set in the ai_interface module
        from ai_interface import set_openrouter_api_key
        key_set = set_openrouter_api_key(api_key)

        if not key_set:
            print("Error: Failed to set OpenRouter API key.")
            return

        # Read the prompt from the file
        with open(prompt_file, 'r', encoding='utf-8') as f:
            prompt = f.read()

        print("Analyzing content from external sources...")
        print("This is a direct LLM analysis that bypasses the RAG system")

        # Get the model name based on the index
        model_names = [
            "mistralai/mistral-7b-instruct",
            "google/gemini-2.0-flash-001",
            "qwen/qwen3-235b-a22b:free",
            "meta-llama/llama-4-maverick:free",
            "google/gemini-2.0-flash-exp:free"
        ]

        model_name = model_names[model_index] if 0 <= model_index < len(model_names) else "mistralai/mistral-7b-instruct"
        print(f"Using model: {model_name}")

        # Create a more specific system message to guide the analysis
        system_message = """You are an expert research assistant tasked with analyzing and synthesizing information from multiple web sources.

Your primary responsibilities are to:
1. Thoroughly analyze ALL provided content from each source
2. Extract key insights, examples, and use cases related to the query
3. Synthesize a comprehensive answer that directly addresses the query
4. Cite specific sources when providing information
5. Identify any conflicting information between sources
6. Categorize information by domain, industry, or use case when relevant
7. Highlight practical applications and real-world examples
8. If the sources don't contain sufficient information, suggest 2-3 additional specific search queries

Format your response with clear sections and include ALL relevant information from the sources.
Start your answer with "Answer:" and end with a "RELEVANT SOURCES" section listing the URLs.
If additional searches are needed, include them in a section called "ADDITIONAL SEARCH QUERIES" after your answer but before the sources.

For queries about RAG (Retrieval-Augmented Generation) systems:
- Focus on identifying specific, real-world applications and use cases
- Categorize use cases by industry or domain
- Highlight the benefits and impact in each scenario
- Note any innovative or unique implementations
- Extract practical insights that directly answer the query

IMPORTANT: Base your analysis ONLY on the content provided in the sources, not on your prior knowledge.
IMPORTANT: Avoid using special characters or emojis in your response to prevent encoding issues."""

        # Use the OpenRouter API directly
        from ai_interface import analyze_text_with_openrouter
        answer = analyze_text_with_openrouter(
            prompt,
            model_name=model_name,
            system_message_content=system_message
        )

        # Log the raw LLM response for debugging
        print("=" * 80)
        print("DEBUG: RAW LLM RESPONSE BEGIN")
        print("=" * 80)
        print(answer)
        print("=" * 80)
        print("DEBUG: RAW LLM RESPONSE END")
        print("=" * 80)

        # Log the length of the response
        print(f"DEBUG: LLM response length: {len(answer)} characters")

        # Ensure the answer starts with "Answer:" for proper UI formatting
        if not answer.startswith("Answer:"):
            print("DEBUG: Adding 'Answer:' prefix to LLM response")
            answer = "Answer: " + answer

        # Clean the answer to remove problematic characters
        # Replace emojis and special characters with their text equivalents or remove them
        import re

        # Function to clean text of problematic characters
        def clean_text(text):
            # Replace common emojis with text equivalents
            text = text.replace('🧠', '[brain]')
            text = text.replace('✨', '[sparkles]')
            text = text.replace('👍', '[thumbs up]')
            text = text.replace('🌿', '[leaf]')
            text = text.replace('💊', '[pill]')
            text = text.replace('🔬', '[microscope]')
            text = text.replace('📚', '[books]')

            # Remove other non-ASCII characters
            text = re.sub(r'[^\x00-\x7F]+', ' ', text)

            return text

        # Clean the answer
        original_length = len(answer)
        answer = clean_text(answer)
        cleaned_length = len(answer)

        print(f"DEBUG: Original length: {original_length}, Cleaned length: {cleaned_length}")
        if original_length != cleaned_length:
            print(f"DEBUG: Removed {original_length - cleaned_length} characters during cleaning")

        # Print the answer - use sys.stdout.buffer.write to handle Unicode
        import sys
        try:
            print("DEBUG: FINAL CLEANED RESPONSE BEGIN")
            print(answer)
            print("DEBUG: FINAL CLEANED RESPONSE END")
        except UnicodeEncodeError:
            # If print fails due to encoding issues, use a safer approach
            print("DEBUG: UnicodeEncodeError occurred, using buffer.write")
            sys.stdout.buffer.write(answer.encode('utf-8'))
            print()  # Add a newline

    except Exception as e:
        print(f"Error in direct analysis: {e}")
        import traceback
        traceback.print_exc()

def query_tools_knowledge(query_text, k=3):
    """
    Query the tools knowledge base for relevant tools and procedures.

    Args:
        query_text: The query text describing what the user wants to do
        k: Number of results to return

    Returns:
        str: Formatted response with relevant tools and procedures
    """
    global tools_manager

    # Make sure the tools manager is initialized
    if not tools_manager.initialized:
        tools_manager.initialize()

    # Query the tools knowledge base
    result = tools_manager.query_tools(query_text, k=k)

    if not result["success"]:
        return f"Answer: {result['message']}"

    # Return the formatted response
    return f"Answer: {result['formatted_response']}"

def analyze_document_with_llm(document_id, query, llm_interface_func=None, llm_model_name=None):
    """
    Analyze a document directly with an LLM, completely independent of the RAG system.
    This function does not use the RAG system in any way.

    Args:
        document_id: ID of the document to analyze
        query: User's query about the document
        llm_interface_func: Function to call the LLM
        llm_model_name: Name of the LLM model to use

    Returns:
        The LLM's response
    """
    try:
        print("=" * 80)
        print("DOCUMENT MODE: Direct document analysis with LLM")
        print("This function COMPLETELY BYPASSES the RAG system")
        print(f"Document ID: {document_id}")
        print(f"Query: {query}")
        print(f"Model: {llm_model_name}")
        print("=" * 80)

        # Get the document content directly from the database
        # This bypasses the RAG system entirely
        conn = sqlite3.connect(SQLITE_DB_FILE)
        cursor = conn.cursor()

        cursor.execute("""
            SELECT extracted_text, filename, file_type
            FROM documents
            WHERE id = ?
        """, (document_id,))

        result = cursor.fetchone()
        conn.close()

        if not result or not result[0]:
            return f"Error: Document with ID {document_id} not found or has no content"

        document_text = result[0]
        document_name = result[1]
        document_type = result[2]

        print(f"Retrieved document: {document_name} ({document_type})")
        print(f"Document length: {len(document_text)} characters")

        # Check if we have an LLM interface function
        if not llm_interface_func:
            return "Error: No LLM interface function provided for document analysis"

        # Create a prompt for the LLM
        # This is a direct document analysis prompt, not using RAG
        prompt = f"""I need you to analyze and answer a question about the following document:

DOCUMENT: {document_name} ({document_type})

USER QUESTION: {query}

DOCUMENT CONTENT:
{document_text}

Please provide a comprehensive answer based ONLY on the information in this document.
If the document doesn't contain information to answer the question, clearly state that.
Do not make up information or use your general knowledge to fill in gaps.
Focus specifically on addressing the user's question using only the document content provided.

Start your answer with "Based on the document:" and include relevant quotes or sections from the document to support your answer.
"""

        print(f"Sending document analysis prompt directly to LLM (length: {len(prompt)} characters)")
        print("This is a DIRECT LLM analysis that completely bypasses the RAG system")

        # Create a system message for the LLM
        system_message = (
            "You are a document analysis assistant. Your task is to analyze documents and answer questions about them. "
            "Only use information contained in the document provided. "
            "If the document doesn't contain the information needed to answer the question, clearly state that. "
            "Do not make up information or use your general knowledge to fill in gaps. "
            "Be precise, thorough, and accurate in your analysis."
        )

        # Call the LLM directly
        # This is a direct call to the LLM, not using RAG
        print("Calling LLM directly with document content (bypassing RAG system)")
        response = llm_interface_func(
            user_content=prompt,
            model_name=llm_model_name,
            system_message_content=system_message,
            site_url="document_analysis"
        )

        print("LLM response received successfully")

        # Add a footer with document information
        response += f"\n\nDocument: {document_name}"

        return response
    except Exception as e:
        print(f"Error analyzing document with LLM: {e}")
        import traceback
        traceback.print_exc()
        return f"Error analyzing document: {str(e)}"

def handle_document_context_command(command_data, rag_manager):
    """
    Handle document context commands from the UI.

    Args:
        command_data: JSON string with document context information
        rag_manager: The RAG manager instance
    """
    try:
        # Parse the command data
        document_info = json.loads(command_data)
        document_id = document_info.get('documentId')

        if not document_id:
            print("Error: No document ID provided in document context command")
            return False

        # Get the document content
        conn = sqlite3.connect(SQLITE_DB_FILE)
        cursor = conn.cursor()

        cursor.execute("""
            SELECT extracted_text
            FROM documents
            WHERE id = ?
        """, (document_id,))

        result = cursor.fetchone()
        conn.close()

        if not result or not result[0]:
            print(f"Error: Document with ID {document_id} not found or has no content")
            return False

        document_text = result[0]

        # Set the document context in the RAG manager
        success = rag_manager.set_document_context(document_text, document_info)

        if success:
            print(f"Document context set: {document_info.get('fileName', 'Unknown document')}")
        else:
            print("Failed to set document context")

        return success
    except Exception as e:
        print(f"Error handling document context command: {e}")
        return False

def handle_tools_query_command(command_str):
    """
    Handle a tools_query command from the UI.

    Args:
        command_str: The command string in the format 'tools_query:query_text:k'

    Returns:
        The query result
    """
    try:
        # Parse the command string
        parts = command_str.replace('tools_query:', '').split(':', 1)

        if len(parts) >= 1:
            query_text = parts[0]
            k = int(parts[1]) if len(parts) > 1 else 3

            print(f"Tools Knowledge Query: {query_text}")
            print(f"Number of results: {k}")

            # Query the tools knowledge base
            result = query_tools_knowledge(query_text, k=k)
            return result
        else:
            return "Error: Invalid tools_query command format"
    except Exception as e:
        print(f"Error handling tools query command: {e}")
        import traceback
        traceback.print_exc()
        return f"Error querying tools knowledge: {str(e)}"

def handle_analyze_document_command(command_str):
    """
    Standalone function to handle the analyze_document command.
    This function is completely independent of the RAG system.

    Args:
        command_str: The analyze_document command string

    Returns:
        The analysis result
    """
    print("=" * 80)
    print("DOCUMENT MODE: Direct document analysis with LLM (completely bypassing RAG)")
    print(f"Command: {command_str}")
    print("=" * 80)

    try:
        # Format: analyze_document:document_id:model_index:query
        parts = command_str.replace('analyze_document:', '').split(':', 2)
        if len(parts) < 3:
            print("ERROR: Invalid analyze_document command format")
            return "Error: Invalid analyze_document command format. Expected format: analyze_document:document_id:model_index:query"

        document_id = parts[0]
        model_index = int(parts[1])
        doc_query = parts[2]

        print(f"DOCUMENT MODE: Analyzing document ID {document_id} with query: {doc_query}")

        # Get the model name based on the index
        model_names = [
            "mistralai/mistral-7b-instruct",
            "google/gemini-2.0-flash-001",
            "qwen/qwen3-235b-a22b:free",
            "meta-llama/llama-4-maverick:free",
            "google/gemini-2.0-flash-exp:free"
        ]

        model_name = model_names[model_index] if 0 <= model_index < len(model_names) else "mistralai/mistral-7b-instruct"

        print(f"DIRECT DOCUMENT ANALYSIS: Analyzing document {document_id} with model {model_name}")
        print(f"Query: {doc_query}")
        print("This is a direct LLM analysis that completely bypasses the RAG system")

        # Import the OpenRouter interface directly
        from ai_interface import analyze_text_with_openrouter, set_openrouter_api_key

        # Make sure we have an API key
        api_key = os.getenv("OPENROUTER_API_KEY")
        if not api_key:
            print("Error: OpenRouter API key not set. Set the OPENROUTER_API_KEY environment variable.")
            return "Error: OpenRouter API key not set. Please set up your API key in settings."

        # Set the API key in the ai_interface module
        key_set = set_openrouter_api_key(api_key)

        if not key_set:
            print("Error: Failed to set OpenRouter API key.")
            return "Error: Failed to set OpenRouter API key."

        # Analyze the document with LLM - DIRECT ANALYSIS, NOT USING RAG
        print("Performing direct document analysis with LLM (bypassing RAG system)")
        answer = analyze_document_with_llm(
            document_id,
            doc_query,
            llm_interface_func=analyze_text_with_openrouter,
            llm_model_name=model_name
        )

        # Ensure the answer starts with "Answer:" for the UI
        if not answer.startswith("Answer:") and not answer.startswith("Based on the document:"):
            answer = "Answer: " + answer

        print(f"Answer:\n{answer}")
        return answer
    except Exception as e:
        import traceback
        traceback.print_exc()
        error_msg = f"Error analyzing document: {str(e)}"
        print(error_msg)
        return f"Answer: {error_msg}"

def get_document_content(document_id):
    """
    Retrieve and print the content of a document by ID.

    Args:
        document_id: ID of the document to retrieve
    """
    # Initialize the database
    init_db()

    # Connect to the database
    conn = sqlite3.connect(SQLITE_DB_FILE)
    cursor = conn.cursor()

    try:
        # Get the document content
        cursor.execute("""
            SELECT extracted_text
            FROM documents
            WHERE id = ?
        """, (document_id,))

        result = cursor.fetchone()

        if result and result[0]:
            # Print the document content in a format that can be parsed by the Electron app
            print(f"document_content:{result[0]}")
        else:
            print(f"Error: Document with ID {document_id} not found or has no content")
    except sqlite3.Error as e:
        print(f"SQLite error retrieving document: {e}")
    finally:
        conn.close()

def get_document_content(document_id, db_path=SQLITE_DB_FILE):
    """
    Get the content of a document from the database.

    Args:
        document_id: ID of the document to retrieve
        db_path: Path to the SQLite database

    Returns:
        str: The document content
    """
    try:
        # Connect to the database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Get the document content
        cursor.execute(
            "SELECT extracted_text FROM documents WHERE id = ?",
            (document_id,)
        )
        result = cursor.fetchone()

        if result:
            # Print the content with a prefix that can be parsed by the Electron app
            print(f"document_content:{result[0]}")
            return result[0]
        else:
            print("document_content:Document not found")
            return None
    except Exception as e:
        print(f"Error retrieving document content: {e}")
        print("document_content:Error retrieving document content")
        return None
    finally:
        if conn:
            conn.close()

def import_documents_cmd(file_paths, options_str=None):
    """
    Command-line function to import documents.

    Args:
        file_paths: List of paths to document files
        options_str: JSON string of import options
    """
    # Initialize the database
    init_db()

    # Parse options if provided
    options = None
    if options_str:
        try:
            options = json.loads(options_str)
        except json.JSONDecodeError:
            print(f"Error parsing options: {options_str}")

    # Import the documents
    results = import_documents(file_paths, options=options)

    # Print results in a format that can be parsed by the Electron app
    if results:
        print(f"documents_imported:{len(results)}")
        for doc in results:
            print(f"document:{doc['id']}:{doc['filename']}:{doc['file_type']}")
    else:
        print("documents_imported:0")

if __name__ == "__main__":
    import sys

    # Check if we're just initializing the database
    if len(sys.argv) > 1 and sys.argv[1] == '--init-db-only':
        # Just initialize the database and exit
        print("Database initialization complete.")
        sys.exit(0)
    # Check if we're checking for existing data
    elif len(sys.argv) > 1 and sys.argv[1] == '--check-data':
        # Check for existing data and exit
        check_existing_data(print_output=True)
        sys.exit(0)
    # Check if we're analyzing existing data
    elif len(sys.argv) > 1 and sys.argv[1] == '--analyze-existing':
        # Analyze existing data
        analyze_existing_data()
        sys.exit(0)
    # Check if we're doing direct analysis
    elif len(sys.argv) > 2 and sys.argv[1] == '--direct-analysis':
        # Perform direct analysis
        prompt_file = sys.argv[2]
        model_index = int(sys.argv[3]) if len(sys.argv) > 3 else 0
        direct_analysis(prompt_file, model_index)
        sys.exit(0)
    # Check if we're importing documents
    elif len(sys.argv) > 2 and sys.argv[1] == '--import-documents':
        # Import documents
        # Check if options are provided
        options_str = None
        file_paths = []

        for arg in sys.argv[2:]:
            if arg.startswith('--options='):
                options_str = arg.replace('--options=', '')
            else:
                file_paths.append(arg)

        import_documents_cmd(file_paths, options_str)
        sys.exit(0)
    # Check if we're retrieving a document
    elif len(sys.argv) > 2 and sys.argv[1] == '--get-document':
        # Get document content
        document_id = sys.argv[2]
        get_document_content(document_id)
        sys.exit(0)
    # Check if we're analyzing a document directly with LLM
    elif len(sys.argv) > 3 and sys.argv[1] == '--analyze-document':
        # Format: --analyze-document document_id query
        document_id = sys.argv[2]
        query = sys.argv[3]

        # Check if API key is set
        api_key = os.getenv("OPENROUTER_API_KEY")
        if not api_key:
            print("Error: OpenRouter API key not set. Set the OPENROUTER_API_KEY environment variable.")
            sys.exit(1)

        # Make sure the API key is also set in the ai_interface module
        from ai_interface import set_openrouter_api_key
        key_set = set_openrouter_api_key(api_key)

        if not key_set:
            print("Error: Failed to set OpenRouter API key.")
            sys.exit(1)

        # Use a default model
        model_name = "mistralai/mistral-7b-instruct"

        # Analyze the document with LLM
        answer = analyze_document_with_llm(
            document_id,
            query,
            llm_interface_func=analyze_text_with_openrouter,
            llm_model_name=model_name
        )

        print(answer)
        sys.exit(0)
    # Check if we're handling an analyze_document command directly
    elif len(sys.argv) > 2 and sys.argv[1] == '--handle-analyze-document':
        # Format: --handle-analyze-document analyze_document:document_id:model_index:query
        command_str = sys.argv[2]
        result = handle_analyze_document_command(command_str)
        print(result)
        sys.exit(0)
    # Check if we're handling a tools query command
    elif len(sys.argv) > 2 and sys.argv[1] == '--handle-tools-query':
        # Format: --handle-tools-query tools_query:query_text:k
        command_str = sys.argv[2]
        result = handle_tools_query_command(command_str)
        print(result)
        sys.exit(0)
    # Check if we're handling a direct RAG query command
    elif len(sys.argv) > 2 and sys.argv[1] == '--handle-rag-direct':
        # Format: --handle-rag-direct rag_direct_query:query:use_llm:model_name
        command_str = sys.argv[2]

        try:
            # Format: rag_direct_query:query:use_llm:model_name
            parts = command_str.replace('rag_direct_query:', '').split(':', 2)
            if len(parts) >= 2:
                query = parts[0]
                use_llm_str = parts[1]
                model_name = parts[2] if len(parts) > 2 else "mistralai/mistral-7b-instruct"

                use_llm = use_llm_str.lower() == 'true'

                print(f"DIRECT RAG QUERY: {query}")
                print(f"Use LLM: {use_llm}")
                print(f"Model: {model_name}")

                # Initialize RAG manager
                rag_manager = RagManager()

                # Check if we have existing data
                has_data = check_existing_data()
                if not has_data:
                    print("No existing data found in the database.")
                    answer = "Error: No data found in the knowledge base. Please crawl a website or import documents first."
                else:
                    # Load existing data into the RAG system
                    print("Loading existing data into RAG system...")
                    rag_manager.load_existing_data()

                    if use_llm:
                        # Make sure we have an API key
                        api_key = os.getenv("OPENROUTER_API_KEY")
                        if not api_key:
                            print("Error: OpenRouter API key not set. Set the OPENROUTER_API_KEY environment variable.")
                            answer = "Error: OpenRouter API key not set. Please set up your API key in settings."
                        else:
                            # Set the API key in the ai_interface module
                            from ai_interface import set_openrouter_api_key
                            key_set = set_openrouter_api_key(api_key)

                            if not key_set:
                                print("Error: Failed to set OpenRouter API key.")
                                answer = "Error: Failed to set OpenRouter API key."
                            else:
                                # Query the RAG system with LLM
                                print("Querying RAG system with LLM...")
                                answer = rag_manager.query_rag(
                                    query,
                                    llm_interface_func=analyze_text_with_openrouter,
                                    llm_model_name=model_name,
                                    use_llm=True
                                )
                    else:
                        # Query the RAG system without LLM
                        print("Querying RAG system without LLM (retrieval only)...")
                        answer = rag_manager.query_rag(
                            query,
                            use_llm=False
                        )

                # Ensure the answer starts with "Answer:" for the UI
                if not answer.startswith("Answer:"):
                    answer = "Answer: " + answer

                print(f"Answer:\n{answer}")
            else:
                print("Error: Invalid rag_direct_query command format")
                sys.exit(1)
        except Exception as e:
            print(f"Error handling direct RAG query: {e}")
            sys.exit(1)
    else:
        # Run the full application
        main()
